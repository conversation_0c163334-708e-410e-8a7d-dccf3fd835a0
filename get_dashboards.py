

import requests
import json


# --- 配置 ---
# 您的 Grafana URL
GRAFANA_URL = "https://yw-telemetry-grafana.ttyuyin.com"
# 将您的 Grafana API 令牌粘贴到这里
GRAFANA_API_KEY = "YOUR_GRAFANA_API_KEY_HERE" 


# --- 脚本 ---

def main():
    """
    主函数，用于获取并保存所有 Grafana 仪表盘。
    """
    # if GRAFANA_API_KEY == "YOUR_GRAFANA_API_KEY_HERE":
    #     print("错误：请在脚本中设置您的 GRAFANA_API_KEY。")
    #     return

    headers = {
        # "Authorization": f"Bearer {GRAFANA_API_KEY}",
        "Content-Type": "application/json",
        "Accept": "application/json"
    }

    # 1. 获取所有仪表盘和文件夹的列表
    search_url = f"{GRAFANA_URL}/api/search?type=dash-db"
    print(f"正在从 {search_url} 获取仪表盘列表...")

    try:
        response = requests.get(search_url, headers=headers, timeout=15)
        response.raise_for_status()  # 如果请求失败 (如 4xx or 5xx), 则会抛出异常
        dashboards = response.json()
    except requests.exceptions.RequestException as e:
        print(f"错误：无法连接到 Grafana 或获取仪表盘列表: {e}")
        return

    if not dashboards:
        print("未找到任何仪表盘。")
        return
        
    print(f"成功找到 {len(dashboards)} 个仪表盘。")

    # 2. 遍历列表，根据UID获取每个仪表盘的详细信息
    for board in dashboards:
        uid = board.get("uid")
        title = board.get("title", "untitled")
        
        if not uid:
            continue

        print(f"--- 获取仪表盘: {title} (UID: {uid}) ---")
        dashboard_url = f"{GRAFANA_URL}/api/dashboards/uid/{uid}"

        try:
            dash_response = requests.get(dashboard_url, headers=headers, timeout=15)
            dash_response.raise_for_status()
            dashboard_data = dash_response.json()
            if uid == "doItiNA7k":
            # 3. 将仪表盘JSON打印到控制台
                print(json.dumps(dashboard_data, ensure_ascii=False, indent=2))
                return
            # https://yw-telemetry-grafana.ttyuyin.com/grafana/render/d-solo/
            #   doItiNA7k/ uid #uid
            # zhu-ji-jian-kong 不重要
            # ?orgId=1
            # &var-database=ThanosQueryFrontend
            # &var-cmdb_id=671712ff5ed39eb1b3486544 #资源ID
            # &from=1750904884766 #时间范围
            # &to=1750908484766 #时间范围
            # &panelId=39 面板ID
            # &width=1000 #默认值
            # &height=500 #默认值
            # &tz=Asia%2FShanghai  #默认值
            print("--- 获取完成 ---\n")


        except requests.exceptions.RequestException as e:
            print(f" -> 获取仪表盘 '{title}' 失败: {e}")

    print("\n所有操作完成。")

if __name__ == "__main__":
    main()

