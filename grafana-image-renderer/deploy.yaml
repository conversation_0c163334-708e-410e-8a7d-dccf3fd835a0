kind: Deployment
apiVersion: apps/v1
metadata:
  name: grafana-image-renderer
  namespace: sre-platform
spec:
  replicas: 1
  selector:
    matchLabels:
      app: grafana-image-renderer
  template:
    metadata:
      labels:
        app: grafana-image-renderer
    spec:
      containers:
        - name: grafana-image-renderer
          image: cr.ttyuyin.com/yunwei/tt-grafana-image-renderer:v1.0
          env:
            - name: HTTP_HOST
              value: "0.0.0.0"
            - name: HTTP_PORT
              value: "80"
          resources:
            limits:
              cpu: 4
              memory: 4Gi
            requests:
              cpu: 1
              memory: 1Gi
          imagePullPolicy: IfNotPresent
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirst
---
kind: Service
apiVersion: v1
metadata:
  name: grafana-image-renderer
  namespace: sre-platform
spec:
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
  selector:
    app: grafana-image-renderer
  type: ClusterIP
